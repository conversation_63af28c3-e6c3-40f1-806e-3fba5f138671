{"mappings": ";AAEA,iBAAiB,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;AAc/C;;;GAGG;AACH,4BAAqB,CAAC,EAAE,GAAG,IAAI,EAAE,YAAY,CAAC,CAAC,EAAE,UACjC,CAAC,UAChB;AAED;;;GAGG;AACH,gCAAyB,CAAC,EAAE,GAAG,IAAI,EAAE,YAAY,CAAC,CAAC,EAAE,qBAGpD", "sources": ["packages/react/compose-refs/src/packages/react/compose-refs/src/composeRefs.tsx", "packages/react/compose-refs/src/packages/react/compose-refs/src/index.ts", "packages/react/compose-refs/src/index.ts"], "sourcesContent": [null, null, "export { composeRefs, useComposedRefs } from './composeRefs';\n"], "names": [], "version": 3, "file": "index.d.ts.map"}